/*
 * 药片装瓶系统顶层模块
 *
 * 功能描述：
 * - 系统的最高层模块，负责连接所有子模块
 * - 处理输入输出信号的分配和连接
 * - 管理时钟和复位信号的分发
 *
 * 作者：数字逻辑课程设计小组
 * 日期：2024-2025春季学期
 */

`include "config.svh"

module top_module (
    // 系统时钟和控制信号
    input logic clock,              // 系统主时钟 (100MHz)
    input logic reset,              // 系统复位信号 (高电平有效)
    input logic strict_enable,      // 严格模式使能信号

    // 用户输入接口
    input logic [4:0] raw_button,   // 5个物理按钮的原始输入
    input logic [3:0] keyboard_row_n, // 4x4矩阵键盘行信号 (低电平有效)

    // 用户输出接口
    output logic [3:0] keyboard_col_n,        // 4x4矩阵键盘列信号 (低电平有效)
    output logic [7:0] digital_tube_enable_n, // 8位数码管使能信号 (低电平有效)
    output logic [6:0] digital_tube_segment_n, // 数码管7段显示信号 (低电平有效)
    output logic digital_tube_dp_n,           // 数码管小数点信号 (低电平有效)

    // 设备控制接口
    output logic buzzer_audio,      // 蜂鸣器音频输出信号
    output logic funnel_disable,    // 漏斗禁用信号 (高电平禁用)
    output logic motor_enable       // 电机使能信号 (高电平使能)
);

    // ========================================
    // 信号定义和基本设置
    // ========================================

    // 复位信号转换：将高电平有效的复位信号转换为低电平有效
    logic reset_n;
    assign reset_n = ~reset;

    // ========================================
    // 输入处理模块实例化
    // ========================================

    // 按钮处理模块：对原始按钮信号进行防抖动处理
    logic [4:0] button;  // 处理后的按钮信号
    buttons b_ins (
        .clock(clock),
        .reset_n(reset_n),
        .raw_button(raw_button),    // 原始按钮输入
        .button(button)             // 防抖动后的按钮输出
    );

    // 4x4矩阵键盘处理模块：扫描键盘并识别按键
    logic [15:0] keyboard;  // 16位键盘状态，每位对应一个按键
    keyboards k_ins (
        .clock(clock),
        .reset_n(reset_n),
        .keyboard_row_n(keyboard_row_n),    // 键盘行输入
        .keyboard_col_n(keyboard_col_n),    // 键盘列输出
        .keyboard(keyboard)                 // 键盘状态输出
    );

    // ========================================
    // 药片模拟器模块
    // ========================================

    // 药片传感器信号和控制信号
    logic pill_pulse;       // 药片检测脉冲信号
    logic pill_disable;     // 药片传送禁用信号
    logic [1:0] speed_select; // 速度选择信号

    // 将内部控制信号连接到外部输出
    assign funnel_disable = pill_disable;      // 漏斗禁用信号
    assign motor_enable = ~pill_disable;       // 电机使能信号（与漏斗禁用相反）

    // 药片模拟器：模拟药片传感器，生成药片检测脉冲
    pill_simulator sim_ins (
        .clock(clock),
        .reset_n(reset_n),
        .strict_enable(strict_enable),      // 严格模式使能
        .pill_disable(pill_disable),        // 药片传送禁用控制
        .speed_select(speed_select),        // 速度选择（来自控制器）
        .pill_pulse(pill_pulse)             // 药片检测脉冲输出
    );

    // ========================================
    // 核心控制器模块
    // ========================================

    // 显示和音频控制信号
    logic [7:0][5:0] display_code;  // 8位数码管显示编码
    logic [31:0] frequency_select;  // 音频频率选择信号

    // 系统核心控制器：处理用户输入，管理系统状态，生成控制信号
    controller c_ins (
        .clock(clock),
        .reset_n(reset_n),
        .keyboard(keyboard),            // 键盘输入
        .button(button),                // 按钮输入
        .pill(pill_pulse),              // 药片检测信号
        .display_code(display_code),    // 数码管显示编码输出
        .display_dp(),                  // 数码管小数点控制（未使用）
        .frequency_select(frequency_select), // 音频频率选择输出
        .funnel_disable(pill_disable),  // 漏斗禁用控制输出
        .speed_select(speed_select)     // 速度选择输出
    );

    // ========================================
    // 显示输出模块
    // ========================================

    // 8位数码管显示控制器：将显示编码转换为数码管驱动信号
    digital_tube dtb_ins (
        .clock(clock),
        .reset_n(reset_n),
        .data_code(display_code),           // 显示数据编码
        .data_dp(8'b0),                     // 小数点控制（全部关闭）
        .enable_n(digital_tube_enable_n),   // 数码管使能输出
        .segment_n(digital_tube_segment_n), // 7段显示输出
        .dp_n(digital_tube_dp_n)            // 小数点输出
    );

    // ========================================
    // 音频输出模块
    // ========================================

    // 音频信号生成器：根据频率选择信号生成相应的音频输出
    audio_generator ag_ins (
        .clock(clock),
        .reset_n(reset_n),
        .frequency_select(frequency_select), // 频率选择输入
        .audio_output(buzzer_audio)          // 蜂鸣器音频输出
    );

endmodule
